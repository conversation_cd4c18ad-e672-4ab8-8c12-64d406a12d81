import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/update_file_metadata.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_widgets/display_video_widget.dart';
import 'package:web_app/main.dart';
import 'package:flutter/foundation.dart'; // For kIsWeb check
import 'package:pdfx/pdfx.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:photo_view/photo_view.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class FileDetailsWidget extends ConsumerStatefulWidget {
  final String fileUrl;

  const FileDetailsWidget({required this.fileUrl, Key? key}) : super(key: key);

  @override
  ConsumerState<FileDetailsWidget> createState() => _FileDetailsWidgetState();
}

class _FileDetailsWidgetState extends ConsumerState<FileDetailsWidget> {
  final TextEditingController _displayNameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _tagnameController = TextEditingController();

  final UniversalWidgets universals = UniversalWidgets();
  final UpdateFileMetadata metadata = UpdateFileMetadata();

  bool _isLoading = true;
  bool _isUpdatingDisplayName = false;
  bool _isUpdatingDescription = false;
  FileMetadata? _fileMetadata;
  String? _documentId;

  @override
  void initState() {
    super.initState();
    _loadFileMetadata();
  }

  Future<void> _loadFileMetadata() async {
    try {
      print("FileDetailsWidget: Loading metadata for URL: ${widget.fileUrl}");

      // Get both the metadata and the document ID
      final querySnapshot = await FirebaseFirestore.instance
          .collection('data')
          .where('File URL', isEqualTo: widget.fileUrl)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        final fileMetadata = FileMetadata.fromFirestore(doc);
        final documentId = doc.id;

        print(
            "FileDetailsWidget: Successfully loaded metadata for file: ${fileMetadata.displayName}");
        print("FileDetailsWidget: Document ID: $documentId");

        if (mounted) {
          setState(() {
            _fileMetadata = fileMetadata;
            _documentId = documentId;
            _displayNameController.text = fileMetadata.displayName;
            _descriptionController.text = fileMetadata.fileDescription;
            _isLoading = false;
          });
          // Set the chosen file URL for tags provider
          print(
              "FileDetailsWidget: Setting chosenFileUrl to: ${widget.fileUrl}");
          ref.read(chosenFileUrl.notifier).state = widget.fileUrl;
        }
      } else {
        print(
            "FileDetailsWidget: No metadata found for URL: ${widget.fileUrl}");
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      print("FileDetailsWidget: Error loading metadata: $e");
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _descriptionController.dispose();
    _tagnameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AsyncValue<List<String>> tagsAsyncValue =
        ref.watch(fetchFileTagsProvider); // Correctly configured to fetch tags

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        title: Text(
          'File Details',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
      ),
      backgroundColor: Colors.grey[50],
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: globals.bookBranchGreen,
              ),
            )
          : _fileMetadata == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      const Text(
                        "Error loading file details",
                        style: TextStyle(color: Colors.red),
                      ),
                    ],
                  ),
                )
              : _buildFileDetailsContent(tagsAsyncValue),
    );
  }

  Widget _buildFileDetailsContent(AsyncValue<List<String>> tagsAsyncValue) {
    final fileMetadata = _fileMetadata!;

    return tagsAsyncValue.when(
      loading: () => Center(
        child: CircularProgressIndicator(
          color: globals.bookBranchGreen,
        ),
      ),
      error: (error, _) => Center(
        child: Text('Error fetching tags: $error'),
      ),
      data: (tags) {
        return Column(
          children: [
            // File Preview Section
            Container(
              height: 250,
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 6,
                    spreadRadius: 0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  onTap: () {
                    // Handling different media types
                    switch (fileMetadata.fileType) {
                      case 'Image':
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                FullScreenMediaView(fileMetadata.fileURL),
                          ),
                        );
                        break;
                      case 'Video':
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => DisplayVideoWidget(
                                videoUrl: fileMetadata.fileURL),
                          ),
                        );
                        break;
                      case 'PDF':
                        if (kIsWeb) {
                          // Navigator.push(
                          //   context,
                          //   MaterialPageRoute(
                          //     builder: (context) => WebPdfViewer(
                          //         pdfUrl: fileMetadata.fileURL),
                          //   ),
                          // );
                        } else {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  MobilePdfViewer(pdfUrl: fileMetadata.fileURL),
                            ),
                          );
                        }
                        break;
                      default:
                        // Handle other file types or invalid data
                        break;
                    }
                  },
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // File preview
                      fileMetadata.fileType == 'Image'
                          ? Image.network(
                              fileMetadata.fileURL,
                              fit: BoxFit.cover,
                            )
                          : fileMetadata.fileType == 'Video'
                              ? Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Image.asset(
                                      'assets/play_image.png',
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withAlpha(100),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.play_arrow,
                                        color: Colors.white,
                                        size: 40,
                                      ),
                                    ),
                                  ],
                                )
                              : fileMetadata.fileType == 'PDF'
                                  ? Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        Image.asset(
                                          'assets/pdf_icon.png',
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.all(16),
                                          decoration: BoxDecoration(
                                            color: Colors.black.withAlpha(100),
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.visibility,
                                            color: Colors.white,
                                            size: 40,
                                          ),
                                        ),
                                      ],
                                    )
                                  : const SizedBox(),

                      // File type indicator
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: globals.bookBranchGreen.withAlpha(200),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            fileMetadata.fileType,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // File Details Section
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Display Name Section
                      _buildSectionHeader('Display Name'),
                      _buildCard(
                        context,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildTextField(
                              "Display Name",
                              _displayNameController,
                              Icons.title_outlined,
                            ),
                            const SizedBox(height: 16),
                            _buildUpdateButton(
                              context,
                              "Update Display Name",
                              _isUpdatingDisplayName,
                              () => _updateDisplayName(),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Description Section
                      _buildSectionHeader('Description'),
                      _buildCard(
                        context,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildTextField(
                              "Description",
                              _descriptionController,
                              Icons.description_outlined,
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),
                            _buildUpdateButton(
                              context,
                              "Update Description",
                              _isUpdatingDescription,
                              () => _updateDescription(),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Tags Section
                      _buildSectionHeader('Tags'),
                      _buildCard(
                        context,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: 8.0,
                              runSpacing: 8.0,
                              children: [
                                ...tags.map((tag) => _buildTagChip(tag)),
                                _buildAddTagButton(context),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Update methods
  Future<void> _updateDisplayName() async {
    if (_isUpdatingDisplayName || _documentId == null) return;

    setState(() {
      _isUpdatingDisplayName = true;
    });

    try {
      print("Attempting to update display name for document: $_documentId");
      print("New display name: ${_displayNameController.text}");

      await metadata.updateFileDisplayNameById(
        _documentId!,
        _displayNameController.text,
        ref,
      );

      // Update local state immediately
      setState(() {
        _fileMetadata = FileMetadata(
          fileDescription: _fileMetadata!.fileDescription,
          fileName: _fileMetadata!.fileName,
          fileType: _fileMetadata!.fileType,
          privacy: _fileMetadata!.privacy,
          projectDescription: _fileMetadata!.projectDescription,
          projectID: _fileMetadata!.projectID,
          projectName: _fileMetadata!.projectName,
          tags: _fileMetadata!.tags,
          displayName: _displayNameController.text,
          fileURL: _fileMetadata!.fileURL,
          filePath: _fileMetadata!.filePath,
          uid: _fileMetadata!.uid,
          timestamp: _fileMetadata!.timestamp,
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: const Text('Display name updated successfully.'),
          backgroundColor: globals.bookBranchGreen,
        ));
      }
    } catch (e) {
      print("Error updating display name: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Failed to update display name: $e'),
          backgroundColor: Colors.red,
        ));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingDisplayName = false;
        });
      }
    }
  }

  Future<void> _updateDescription() async {
    if (_isUpdatingDescription || _documentId == null) return;

    setState(() {
      _isUpdatingDescription = true;
    });

    try {
      print("Attempting to update description for document: $_documentId");
      print("New description: ${_descriptionController.text}");

      await metadata.updateFileDescriptionById(
        _documentId!,
        _descriptionController.text,
        ref,
      );

      // Update local state immediately
      setState(() {
        _fileMetadata = FileMetadata(
          fileDescription: _descriptionController.text,
          fileName: _fileMetadata!.fileName,
          fileType: _fileMetadata!.fileType,
          privacy: _fileMetadata!.privacy,
          projectDescription: _fileMetadata!.projectDescription,
          projectID: _fileMetadata!.projectID,
          projectName: _fileMetadata!.projectName,
          tags: _fileMetadata!.tags,
          displayName: _fileMetadata!.displayName,
          fileURL: _fileMetadata!.fileURL,
          filePath: _fileMetadata!.filePath,
          uid: _fileMetadata!.uid,
          timestamp: _fileMetadata!.timestamp,
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: const Text('File description updated successfully.'),
          backgroundColor: globals.bookBranchGreen,
        ));
      }
    } catch (e) {
      print("Error updating description: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Failed to update file description: $e'),
          backgroundColor: Colors.red,
        ));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingDescription = false;
        });
      }
    }
  }

  // Helper methods for UI components
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
          color: Colors.grey.shade700,
        ),
      ),
    );
  }

  Widget _buildCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  Widget _buildTextField(
      String label, TextEditingController controller, IconData icon,
      {int maxLines = 1}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: globals.bookBranchGreen),
            ),
            prefixIcon: Icon(icon, color: Colors.grey.shade600),
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateButton(BuildContext context, String label, bool isLoading,
      VoidCallback onPressed) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: globals.bookBranchGreen,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(label),
      ),
    );
  }

  Widget _buildTagChip(String tag) {
    return Chip(
      label: Text(tag),
      backgroundColor: globals.bookBranchGreen.withAlpha(30),
      labelStyle: TextStyle(color: globals.bookBranchGreen),
      deleteIconColor: globals.bookBranchGreen,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(color: globals.bookBranchGreen.withAlpha(100)),
      ),
      onDeleted: () async {
        if (_documentId == null) return;

        try {
          print("Attempting to remove tag '$tag' from document: $_documentId");
          await metadata.removeFileTagFromFirestoreById(_documentId!, tag);
          ref.invalidate(fetchFileTagsProvider);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Tag "$tag" removed successfully.'),
              backgroundColor: globals.bookBranchGreen,
            ));
          }
        } catch (e) {
          print("Error removing tag: $e");
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text('Failed to remove tag: $e'),
              backgroundColor: Colors.red,
            ));
          }
        }
      },
    );
  }

  Widget _buildAddTagButton(BuildContext context) {
    return ActionChip(
      avatar: const Icon(Icons.add, size: 18, color: Colors.white),
      label: const Text('Add Tag'),
      backgroundColor: globals.bookBranchGreen,
      labelStyle: const TextStyle(color: Colors.white),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      onPressed: () {
        if (_documentId == null) return;

        universals.showCustomPopupWithTextfield(
            context, "Add a Tag", "Write a tag name here", _tagnameController,
            () async {
          try {
            final tagName = _tagnameController.text;
            print("Attempting to add tag '$tagName' to document: $_documentId");
            await metadata.uploadFileTagToFirestoreById(_documentId!, tagName);
            _tagnameController.clear();
            ref.invalidate(fetchFileTagsProvider);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('Tag "$tagName" added successfully.'),
                backgroundColor: globals.bookBranchGreen,
              ));
            }
          } catch (e) {
            print("Error adding tag: $e");
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                content: Text('Failed to add tag: $e'),
                backgroundColor: Colors.red,
              ));
            }
          }
        });
      },
    );
  }
}

class FullScreenMediaView extends StatelessWidget {
  final String imageUrl;

  const FullScreenMediaView(this.imageUrl, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        title: Text(
          'Image Viewer',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
      ),
      body: Container(
        color: Colors.black.withAlpha(25),
        child: PhotoView(
          imageProvider: NetworkImage(imageUrl),
          backgroundDecoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          minScale: PhotoViewComputedScale.contained * 0.8,
          maxScale: PhotoViewComputedScale.covered * 2.0,
          initialScale: PhotoViewComputedScale.contained,
          enableRotation: false,
          loadingBuilder: (context, event) => Center(
            child: CircularProgressIndicator(
              color: globals.bookBranchGreen,
              value: event == null
                  ? 0
                  : event.cumulativeBytesLoaded /
                      (event.expectedTotalBytes ?? 1),
            ),
          ),
        ),
      ),
    );
  }
}

class MobilePdfViewer extends StatefulWidget {
  final String pdfUrl;

  const MobilePdfViewer({required this.pdfUrl, Key? key}) : super(key: key);

  @override
  State<MobilePdfViewer> createState() => _MobilePdfViewerState();
}

class _MobilePdfViewerState extends State<MobilePdfViewer> {
  PdfControllerPinch? _pdfController;
  int _totalPages = 0;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadPdfFromUrl(widget.pdfUrl);
  }

  Future<void> _loadPdfFromUrl(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        // Create the PdfControllerPinch with the future directly
        _pdfController = PdfControllerPinch(
          document: PdfDocument.openData(response.bodyBytes),
        );
        // Set the controller and fetch the pages count
        setState(() {
          _pdfController!.document.then((document) {
            // Access pagesCount directly
            int count = document.pagesCount;
            setState(() {
              _totalPages = count;
            });
          });
        });
      } else {
        throw Exception('Failed to load PDF');
      }
    } catch (e) {
      // Using debugPrint instead of print for better logging
      debugPrint('Error loading PDF: $e');
    }
  }

  @override
  void dispose() {
    _pdfController?.dispose();
    super.dispose();
  }

  void _jumpToPage(int page) {
    if (page >= 0 && page < _totalPages) {
      _pdfController?.jumpToPage(page);
      setState(() {
        _currentPage = page;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        title: Text(
          'PDF Viewer',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
      ),
      body: _pdfController != null
          ? PdfViewPinch(
              controller: _pdfController!,
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page; // Internally handle as zero-based index
                });
              },
            )
          : Center(
              child: CircularProgressIndicator(
                color: globals.bookBranchGreen,
              ),
            ),
      bottomNavigationBar: _pdfController != null
          ? _buildBottomNavigationBar()
          : const SizedBox.shrink(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
        border: Border(
          top: BorderSide(
            color: globals.bookBranchGreen.withAlpha(51),
            width: 1,
          ),
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavButton(
            Icons.first_page,
            () => _jumpToPage(0),
            isDisabled: _currentPage <= 0,
          ),
          _buildNavButton(
            Icons.arrow_back,
            () => _jumpToPage(_currentPage - 1),
            isDisabled: _currentPage <= 0,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: globals.bookBranchGreen.withAlpha(25),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Page ${_currentPage + 1} of $_totalPages',
              style: TextStyle(
                color: globals.bookBranchGreen,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildNavButton(
            Icons.arrow_forward,
            () => _jumpToPage(_currentPage + 1),
            isDisabled: _currentPage >= _totalPages - 1,
          ),
          _buildNavButton(
            Icons.last_page,
            () => _jumpToPage(_totalPages - 1),
            isDisabled: _currentPage >= _totalPages - 1,
          ),
        ],
      ),
    );
  }

  Widget _buildNavButton(IconData icon, VoidCallback onPressed,
      {bool isDisabled = false}) {
    return IconButton(
      icon: Icon(icon),
      onPressed: isDisabled ? null : onPressed,
      color: isDisabled ? Colors.grey.shade400 : globals.bookBranchGreen,
      splashRadius: 24,
    );
  }
}
