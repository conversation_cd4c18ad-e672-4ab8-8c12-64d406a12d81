import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:web_app/main.dart';

// the FileMetadata object is obviously not used for fetching per se, but the rest of the methods are used for fetching data
class FileMetadata {
  final String fileDescription;
  final String fileName;
  final String fileType;
  final String privacy;
  final String projectDescription;
  final String projectID;
  final String projectName;
  final List<dynamic> tags;
  final String displayName;
  final String fileURL;
  final String? filePath;
  final String uid;
  final Timestamp? timestamp;

  FileMetadata(
      {required this.fileDescription,
      required this.fileName,
      required this.fileType,
      required this.privacy,
      required this.projectDescription,
      required this.projectID,
      required this.projectName,
      required this.tags,
      required this.displayName,
      required this.fileURL,
      required this.filePath,
      required this.uid,
      this.timestamp});

  // Factory constructor to create a FileMetadata instance from a Firestore document.
  factory FileMetadata.fromFirestore(DocumentSnapshot doc) {
    Map data = doc.data() as Map;
    return FileMetadata(
        fileDescription: data['fileDescription'] ?? '',
        fileName: data['fileName'] ?? '',
        fileType: data['fileType'] ?? '',
        privacy: data['privacy'] ?? '',
        projectDescription: data['projectDescription'] ?? '',
        projectID: data['projectID'] ?? '',
        projectName: data['projectName'] ?? '',
        tags: data['tags'] ?? '',
        displayName: data['displayName'] ?? '',
        fileURL: data['File URL'] ?? '',
        filePath: data['filePath'] ?? '',
        uid: data['uid'] ?? '',
        timestamp: data['timestamp']);
  }

  static Future<List<FileMetadata>> fetchFileMetadataChapterLevel(
      String currentChapter) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentChapter)
        .orderBy('timestamp', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  static Future<List<FileMetadata>> fetchFileMetadataEPublishingChapterLevel(
      String currentChapter) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentEPublishingChapter)
        .orderBy('timestamp', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  static Future<List<FileMetadata>> fetchFileMetadata(String currentChapter,
      String currentSubsection, String currentProjectID) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentChapter)
        .where('subsection', isEqualTo: currentSubsection)
        .where('projectID', isEqualTo: currentProjectID)
        .orderBy('timestamp', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  static Future<List<FileMetadata>> fetchFileMetadataEPublishing(
      String currentEPublishingChapter,
      String currentEPublishingSubsection,
      String currentProjectID) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('chapter', isEqualTo: currentEPublishingChapter)
        .where('subsection', isEqualTo: currentEPublishingSubsection)
        .where('projectID', isEqualTo: currentProjectID)
        .orderBy('timestamp', descending: true)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

// more specific
  static Future<List<FileMetadata>> fetchChosenFileMetadata(
      String chosenFileURL) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('File URL', isEqualTo: chosenFileURL)
        .get();

    return querySnapshot.docs
        .map((doc) => FileMetadata.fromFirestore(doc))
        .toList();
  }

  // Method to get file metadata by URL
  static Future<FileMetadata?> fetchSingleFileMetadataByUrl(
      String fileUrl) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('File URL', isEqualTo: fileUrl)
        .get();

    // Check if at least one document was found
    if (querySnapshot.docs.isNotEmpty) {
      // Return the first document's data as a FileMetadata object
      return FileMetadata.fromFirestore(querySnapshot.docs.first);
    } else {
      // Return null if no document matches the URL
      return null;
    }
  }

  static Future<List<dynamic>> fetchTagsByFileUrl(String fileUrl) async {
    FirebaseFirestore firestore = FirebaseFirestore.instance;
    QuerySnapshot querySnapshot = await firestore
        .collection('data')
        .where('File URL', isEqualTo: fileUrl)
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      // Assuming the 'tags' field is stored as a list in the Firestore document
      List<dynamic> tags = querySnapshot.docs.first.get('tags');
      return tags;
    } else {
      // Return an empty list if no document is found
      return [];
    }
  }

  static Future<String?> fetchDocumentIdByFileUrl(
      String fileUrl, FirebaseFirestore db) async {
    var querySnapshot = await db
        .collection('data')
        .where('File URL', isEqualTo: fileUrl)
        .limit(1) // Assuming uniqueness for optimization
        .get();

    if (querySnapshot.docs.isNotEmpty) {
      return querySnapshot.docs.first.id; // Return the document ID
    }
    return null; // Return null if no document is found
  }
}
